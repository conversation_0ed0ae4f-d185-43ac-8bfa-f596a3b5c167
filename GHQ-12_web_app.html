<!doctype html>
<html lang="es">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>GHQ-12 — Cuestionario</title>
  <link rel="preconnect" href="https://fonts.gstatic.com">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&display=swap" rel="stylesheet">
  <style>
    :root{
      --bg:#0f1724;
      --card:#0b1220;
      --muted:#9aa4b2;
      --accent:#06b6d4;
      --accent-2:#7c3aed;
      --glass: rgba(255,255,255,0.04);
      --success:#16a34a;
      --danger:#ef4444;
      color-scheme: dark;
    }
    *{box-sizing:border-box}
    html,body{height:100%; margin:0; font-family:Inter,system-ui,-apple-system,'Segoe UI',<PERSON><PERSON>,'Helvetica Neue',Arial; background:linear-gradient(180deg,#071021 0%, #0b1220 100%); color:#e6eef6}
    .container{max-width:960px; margin:28px auto; padding:22px}
    header{display:flex;align-items:center;gap:16px;margin-bottom:18px}
    .logo{width:64px;height:64px;border-radius:12px;display:flex;align-items:center;justify-content:center;background:linear-gradient(135deg,var(--accent),var(--accent-2));font-weight:700;color:white;font-size:20px}
    h1{font-size:20px;margin:0}
    p.lead{margin:6px 0 0;color:var(--muted)}

    .card{background:linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0.01)); padding:18px;border-radius:12px;box-shadow:0 6px 18px rgba(2,6,23,0.6);}
    .grid{display:grid;grid-template-columns:1fr 320px;gap:18px}
    @media (max-width:880px){.grid{grid-template-columns:1fr}}

    ol{padding-left:14px;margin:0}
    .question{background:var(--glass); padding:12px;border-radius:8px;margin-bottom:10px}
    .q-text{font-weight:600}
    .radios{display:flex;gap:8px;margin-top:8px;flex-wrap:wrap}
    label.choice{display:flex;align-items:center;gap:8px;background:rgba(255,255,255,0.02);padding:8px 10px;border-radius:8px;cursor:pointer;border:1px solid transparent}
    label.choice input{appearance:none}
    label.choice:hover{border-color:rgba(255,255,255,0.04)}
    .scale-desc{font-size:13px;color:var(--muted);margin-top:6px}

    .side{position:sticky;top:20px;height:max-content}
    .result{margin-top:8px;padding:12px;border-radius:8px;background:linear-gradient(180deg, rgba(255,255,255,0.01), rgba(255,255,255,0.00))}
    .score{font-size:34px;font-weight:700; margin:4px 0}
    .bar{height:10px;background:rgba(255,255,255,0.05);border-radius:999px;overflow:hidden}
    .bar-fill{height:100%;width:0%;background:linear-gradient(90deg,var(--accent),var(--accent-2));transition:width .6s ease}
    .muted{color:var(--muted);font-size:13px}

    .actions{display:flex;gap:8px;margin-top:12px}
    button{background:var(--accent);border:none;color:white;padding:10px 12px;border-radius:8px;cursor:pointer;font-weight:600}
    button.secondary{background:transparent;border:1px solid rgba(255,255,255,0.06)}
    button.danger{background:var(--danger)}
    footer{margin-top:18px;color:var(--muted);font-size:13px;text-align:center}

    .note{font-size:13px;color:var(--muted);margin-top:8px}
    .json-output, .interpret{white-space:pre-wrap;font-size:14px}

    /* small accessibility tweaks */
    label.choice input[type="radio"]{width:16px;height:16px;border-radius:50%;border:2px solid rgba(255,255,255,0.08);margin-right:6px}
    label.choice input[type="radio"]:checked{background:linear-gradient(90deg,var(--accent),var(--accent-2));border-color:transparent}
  </style>
</head>
<body>
  <div class="container">
    <header>
      <div class="logo">GHQ</div>
      <div>
        <h1>GHQ-12 — Evaluación breve de malestar psicológico</h1>
        <p class="lead">Contesta según cómo te sentiste en las <strong>últimas semanas</strong>.</p>
      </div>
    </header>

    <div class="card grid">
      <main>
        <form id="ghqForm">
          <div class="scale-desc">Escala: 0 = Mejor que lo habitual · 1 = Igual · 2 = Menos · 3 = Mucho menos</div>
          <ol id="questionsList">
          </ol>

          <div style="display:flex;gap:8px;margin-top:12px;align-items:center;">
            <button type="button" id="calcBtn">Calcular resultados</button>
            <button type="button" class="secondary" id="resetBtn">Borrar respuestas</button>
            <div style="flex:1"></div>
            <button type="button" id="downloadBtn">Descargar JSON</button>
            <button type="button" class="secondary" id="printBtn">Imprimir / Guardar PDF</button>
          </div>
        </form>

        <p class="note">Uso sugerido: sumar la puntuación (0–36). También calculamos la puntuación bimodal (0–12) donde 0–1 = 0, 2–3 = 1 por ítem.</p>
      </main>

      <aside class="side">
        <div class="result card">
          <div class="muted">Puntuación (suma simple)</div>
          <div class="score" id="sumScore">— / 36</div>
          <div class="bar"><div id="barFill" class="bar-fill"></div></div>
          <div class="muted" id="sumInterpret">Interpretación: —</div>

          <hr style="opacity:.06;margin:12px 0">

          <div class="muted">Puntuación (método bimodal)</div>
          <div class="score" id="bimodalScore">— / 12</div>
          <div class="muted" id="bimodalInterpret">Interpretación: —</div>

          <div class="actions">
            <button id="detailsBtn" class="secondary" type="button">Ver JSON</button>
            <button id="copyBtn" class="secondary" type="button">Copiar resultado</button>
            <button id="helpBtn" class="secondary" type="button">Ayuda breve</button>
          </div>
        </div>

        <div class="result card" style="margin-top:10px">
          <div class="muted">Informe / Interpretación</div>
          <div class="interpret" id="interpretText">Aún no calculado.</div>
        </div>
      </aside>
    </div>

    <footer>Creado por tu asistente — Si deseas integración (Google Forms, Excel, exportar a PDF automatizado), dime y lo preparo.</footer>
  </div>

  <script>
    // Preguntas (texto en afirmaciones, en el orden del GHQ-12 adaptado)
    const questions = [
      'He podido concentrarme bien en mi trabajo y en mis tareas diarias.',
      'Mis preocupaciones me han quitado el sueño.',
      'Me siento útil en mi vida y en el trabajo.',
      'Me he sentido capaz de tomar decisiones con claridad.',
      'Me he sentido nervioso(a) o muy tenso(a) con frecuencia.',
      'He sentido que no puedo superar las dificultades que tengo.',
      'He disfrutado de mis actividades habituales del día a día.',
      'He sido capaz de afrontar mis problemas de forma adecuada.',
      'Me he sentido triste o deprimido(a).',
      'He perdido confianza en mí mismo(a).',
      'He pensado que no valgo para nada.',
      'Me he sentido razonablemente feliz considerando mi situación.'
    ];

    const qList = document.getElementById('questionsList');
    questions.forEach((q,i)=>{
      const li = document.createElement('li');
      li.className = 'question';
      li.innerHTML = `
        <div class="q-text">${i+1}. ${q}</div>
        <div class="radios">
          <label class="choice"><input type="radio" name="q${i}" value="0" aria-label="0"> 0</label>
          <label class="choice"><input type="radio" name="q${i}" value="1" aria-label="1"> 1</label>
          <label class="choice"><input type="radio" name="q${i}" value="2" aria-label="2"> 2</label>
          <label class="choice"><input type="radio" name="q${i}" value="3" aria-label="3"> 3</label>
        </div>
      `;
      qList.appendChild(li);
    });

    function getAnswers(){
      const answers = [];
      for(let i=0;i<questions.length;i++){
        const v = document.querySelector(`input[name=q${i}]:checked`);
        answers.push(v? Number(v.value) : null);
      }
      return answers;
    }

    function interpretSum(score){
      // Interpretaciones sugeridas (ajustables por el profesional)
      if(score <= 11) return {label:'Bajo malestar', detail:'Puntuación baja; no se evidencian signos importantes de malestar psicológico.'};
      if(score <= 20) return {label:'Malestar moderado', detail:'Nivel moderado de malestar. Se recomienda seguimiento y medidas de autocuidado.'};
      return {label:'Alto malestar', detail:'Puntuación alta; valorar intervención profesional y seguimiento clínico.'};
    }

    function interpretBimodal(score){
      // En GHQ bimodal (0-12) frecuentemente corte >=3 o >=4 indica caso probable — se deja a criterio
      if(score <= 2) return {label:'Bajo', detail:'Pocos síntomas según el método bimodal.'};
      if(score <= 5) return {label:'Moderado', detail:'Síntomas moderados según el método bimodal.'};
      return {label:'Alto', detail:'Elevado número de síntomas. Evaluar con profesional.'};
    }

    function calculate(){
      const answers = getAnswers();
      if(answers.some(a=>a===null)){
        alert('Por favor responde todas las afirmaciones antes de calcular.');
        return null;
      }

      const sum = answers.reduce((s,v)=>s+v,0);
      // bimodal: 0 or 1 -> 0 ; 2 or 3 -> 1
      const bimodal = answers.reduce((s,v)=> s + (v>=2?1:0) , 0);

      return {answers, sum, bimodal};
    }

    function renderResults(res){
      const sumScoreEl = document.getElementById('sumScore');
      const bimodalEl = document.getElementById('bimodalScore');
      const bar = document.getElementById('barFill');
      const sumInterpret = document.getElementById('sumInterpret');
      const bimodalInterpret = document.getElementById('bimodalInterpret');
      const interpretText = document.getElementById('interpretText');

      sumScoreEl.textContent = `${res.sum} / 36`;
      const pct = Math.round((res.sum / 36) * 100);
      bar.style.width = pct + '%';

      const sumInt = interpretSum(res.sum);
      sumInterpret.textContent = `Interpretación: ${sumInt.label}`;

      bimodalEl.textContent = `${res.bimodal} / 12`;
      const bimInt = interpretBimodal(res.bimodal);
      bimodalInterpret.textContent = `Interpretación: ${bimInt.label}`;

      // Informe breve automatizado
      interpretText.textContent = `Resumen automático:\nPuntuación total: ${res.sum} / 36 (${sumInt.label}). ${sumInt.detail}\n\nPuntuación bimodal: ${res.bimodal} / 12 (${bimInt.label}). ${bimInt.detail}\n\nRecomendación: ${res.sum>20 || res.bimodal>5 ? 'Considerar evaluación clínica con profesional de la salud mental.' : 'Autocuidado, técnicas de relajación, y reevaluación en semanas.'}`;
    }

    document.getElementById('calcBtn').addEventListener('click', ()=>{
      const r = calculate();
      if(r) renderResults(r);
    });

    document.getElementById('resetBtn').addEventListener('click', ()=>{
      if(!confirm('Borrar todas las respuestas?')) return;
      document.querySelectorAll('input[type=radio]').forEach(i=>i.checked=false);
      document.getElementById('sumScore').textContent = '— / 36';
      document.getElementById('bimodalScore').textContent = '— / 12';
      document.getElementById('interpretText').textContent = 'Aún no calculado.';
      document.getElementById('barFill').style.width = '0%';
      document.getElementById('sumInterpret').textContent = 'Interpretación: —';
      document.getElementById('bimodalInterpret').textContent = 'Interpretación: —';
    });

    document.getElementById('downloadBtn').addEventListener('click', ()=>{
      const r = calculate();
      if(!r) return;
      const payload = {
        timestamp: new Date().toISOString(),
        answers: r.answers,
        sum: r.sum,
        bimodal: r.bimodal
      };
      const dataStr = 'data:text/json;charset=utf-8,' + encodeURIComponent(JSON.stringify(payload, null, 2));
      const a = document.createElement('a'); a.href = dataStr; a.download = `GHQ12_result_${new Date().toISOString().slice(0,10)}.json`; a.click();
    });

    document.getElementById('printBtn').addEventListener('click', ()=>{
      const r = calculate();
      if(!r) return;
      // preparar una vista imprimible simple
      const printWindow = window.open('', '', 'width=800,height=900');
      printWindow.document.write('<pre style="font-family:Inter,Arial;">');
      printWindow.document.write('GHQ-12 - Resultado\n');
      printWindow.document.write('Fecha: ' + new Date().toLocaleString() + '\n\n');
      printWindow.document.write('Respuestas: ' + JSON.stringify(r.answers) + '\n');
      printWindow.document.write('Puntuación total: ' + r.sum + ' / 36\n');
      printWindow.document.write('Puntuación bimodal: ' + r.bimodal + ' / 12\n\n');
      printWindow.document.write(document.getElementById('interpretText').textContent);
      printWindow.document.write('</pre>');
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
    });

    document.getElementById('detailsBtn').addEventListener('click', ()=>{
      const r = calculate();
      if(!r) return;
      alert(JSON.stringify({answers:r.answers, sum:r.sum, bimodal:r.bimodal}, null, 2));
    });

    document.getElementById('copyBtn').addEventListener('click', async ()=>{
      const r = calculate();
      if(!r) return;
      const text = `GHQ-12 Resultado:\nFecha: ${new Date().toLocaleString()}\nPuntuación: ${r.sum}/36\nBimodal: ${r.bimodal}/12\n`;
      try{ await navigator.clipboard.writeText(text); alert('Resultado copiado al portapapeles.'); }
      catch(e){ alert('No se pudo copiar automáticamente. Puedes descargar o imprimir.'); }
    });

    document.getElementById('helpBtn').addEventListener('click', ()=>{
      alert('Interpretación sugerida:\n- Suma simple (0–36): 0–11 Bajo; 12–20 Moderado; 21–36 Alto.\n- Bimodal (0–12): cortes dependientes del contexto; >3 puede indicar caso probable.\nEstas interpretaciones son orientativas; el profesional debe ajustar los puntos de corte.');
    });

  </script>
</body>
</html>
